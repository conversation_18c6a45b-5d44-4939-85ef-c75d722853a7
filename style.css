


Reset and Base Styles
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(102, 126, 234, 0.95);
    backdrop-filter: blur(15px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #ff6b6b;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #ffffff;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.nav-link:hover,
.nav-link.active {
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ff6b6b, #feca57);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff08" points="0,0 1000,300 1000,1000 0,700"/></svg>');
    background-size: cover;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-text {
    color: white;
    animation: fadeInUp 1s ease-out;
}

.greeting {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.name {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title {
    font-size: 2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.highlight {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border: 2px solid #ff6b6b;
    background: rgba(255, 255, 255, 0.1);
    color: #ff6b6b;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-icon:hover {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: #ffffff;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.cta-button {
    display: inline-block;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: #ffffff;
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    border: none;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
    background: linear-gradient(45deg, #feca57, #ff6b6b);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out;
}

.image-container {
    position: relative;
}

.profile-image {
    position: relative;
    z-index: 2;
}

.profile-image::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, #ff6b6b, #feca57, #48cae4);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 3s linear infinite;
}

.profile-image img {
    border: 5px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* About Section */
.about {
    padding: 80px 0;
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    transform: translateX(-50%);
    border-radius: 2px;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-text {
    text-align: center;
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 3rem;
    color: #ffffff;
}

.skills {
    display: grid;
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.skill {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.skill:hover {
    transform: translateY(-5px);
}

.skill-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    display: block;
}

.skill-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 2s ease;
}

/* Services Section */
.services {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.services .section-title {
    color: white;
}

.services .section-title::after {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.service-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.service-icon {
    font-size: 3rem;
    color: #FFD700;
    margin-bottom: 1rem;
}

.service-card h3 {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.service-card p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

/* Portfolio Section */
.portfolio {
    padding: 80px 0;
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.portfolio-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.portfolio-image {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
}

.portfolio-content {
    padding: 1.5rem;
}

.portfolio-content h3 {
    color: #ffffff;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.portfolio-content p {
    color: #ffffff;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.contact .section-title {
    color: white;
}

.contact .section-title::after {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
    font-size: 1.1rem;
}

.contact-item i {
    font-size: 1.5rem;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    width: 30px;
}

.contact-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    background: white;
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);
}

.submit-btn {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #333;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* Footer */
.footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem 0;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .name {
        font-size: 2.5rem;
    }
    
    .title {
        font-size: 1.5rem;
    }
    
    .nav-list {
        flex-direction: column;
        gap: 1rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
    }
    
    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero {
        padding-top: 60px;
    }
    
    .name {
        font-size: 2rem;
    }
    
    .hero-text {
        padding: 1rem;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* Scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
}















/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(102, 126, 234, 0.95);
    backdrop-filter: blur(15px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #ff6b6b;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #ffffff;
    font-weight: 500;
    position: relative;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.nav-link:hover,
.nav-link.active {
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ff6b6b, #feca57);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 80%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff08" points="0,0 1000,300 1000,1000 0,700"/></svg>');
    background-size: cover;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-text {
    color: white;
    animation: fadeInUp 1s ease-out;
}

.greeting {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.name {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title {
    font-size: 2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.highlight {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.description {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border: 2px solid #ff6b6b;
    background: rgba(255, 255, 255, 0.1);
    color: #ff6b6b;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.social-icon:hover {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: #ffffff;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.cta-button {
    display: inline-block;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: #ffffff;
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    border: none;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
    background: linear-gradient(45deg, #feca57, #ff6b6b);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out;
}

.image-container {
    position: relative;
}

.profile-image {
    position: relative;
    z-index: 2;
}

.profile-image::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, #ff6b6b, #feca57, #48cae4);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 3s linear infinite;
}

.profile-image img {
    border: 5px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* About Section */
.about {
    padding: 80px 0;
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    transform: translateX(-50%);
    border-radius: 2px;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
}

.about-text {
    text-align: center;
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 3rem;
    color: #ffffff;
}

.skills {
    display: grid;
    gap: 2rem;
    max-width: 600px;
    margin: 0 auto;
}

.skill {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.skill:hover {
    transform: translateY(-5px);
}

.skill-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    display: block;
}

.skill-bar {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 2s ease;
}

/* Services Section */
.services {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.services .section-title {
    color: white;
}

.services .section-title::after {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.service-card:hover {
    transform: translateY(-10px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.service-icon {
    font-size: 3rem;
    color: #FFD700;
    margin-bottom: 1rem;
}

.service-card h3 {
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.service-card p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
}

/* Portfolio Section */
.portfolio {
    padding: 80px 0;
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.portfolio-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.portfolio-image {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
}

.portfolio-content {
    padding: 1.5rem;
}

.portfolio-content h3 {
    color: #ffffff;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.portfolio-content p {
    color: #ffffff;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.contact .section-title {
    color: white;
}

.contact .section-title::after {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
    font-size: 1.1rem;
}

.contact-item i {
    font-size: 1.5rem;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    width: 30px;
}

.contact-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    background: white;
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);
}

.submit-btn {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #333;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* Footer */
.footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem 0;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }
    
    .name {
        font-size: 2.5rem;
    }
    
    .title {
        font-size: 1.5rem;
    }
    
    .nav-list {
        flex-direction: column;
        gap: 1rem;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
    }
    
    .section-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }
    
    .hero {
        padding-top: 60px;
    }
    
    .name {
        font-size: 2rem;
    }
    
    .hero-text {
        padding: 1rem;
    }
    
    .social-links {
        justify-content: center;
    }
}

/* Scroll behavior */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
}