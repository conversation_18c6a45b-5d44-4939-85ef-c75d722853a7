Reset and Base Styles
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background-color: #1a1a2e;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background-color: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 15px 0;
    border-bottom: 1px solid #16213e;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h2 {
    color: #00d4ff;
    font-size: 24px;
    font-weight: bold;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #00d4ff;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: #00d4ff;
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-text {
    animation: fadeInLeft 1s ease-out;
}

.greeting {
    font-size: 28px;
    color: #ffffff;
    margin-bottom: 10px;
    font-weight: normal;
}

.name {
    font-size: 52px;
    color: #ffffff;
    margin-bottom: 15px;
    font-weight: bold;
    letter-spacing: -1px;
}

.title {
    font-size: 32px;
    color: #ffffff;
    margin-bottom: 25px;
    font-weight: 600;
}

.highlight {
    color: #00d4ff;
    font-weight: bold;
}

.description {
    font-size: 18px;
    color: #b0b0b0;
    margin-bottom: 30px;
    line-height: 1.8;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
}

.social-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: 2px solid #00d4ff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #00d4ff;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background-color: #00d4ff;
    color: #1a1a2e;
    transform: translateY(-3px);
}

.cta-button {
    display: inline-block;
    background-color: #00d4ff;
    color: #1a1a2e;
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out;
}

.image-container {
    position: relative;
    width: 350px;
    height: 350px;
}

.profile-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 120px;
    color: #1a1a2e;
    position: relative;
    overflow: hidden;
}

.profile-image::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, #00d4ff, transparent);
    animation: rotate 3s linear infinite;
}

.profile-image::after {
    content: '';
    position: absolute;
    inset: 5px;
    border-radius: 50%;
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    z-index: 1;
}

.profile-image i {
    position: relative;
    z-index: 2;
}

/* Section Styles */
.section-title {
    font-size: 42px;
    text-align: center;
    margin-bottom: 60px;
    color: #ffffff;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background-color: #00d4ff;
    border-radius: 2px;
}

/* About Section */
.about {
    padding: 100px 0;
    background-color: #16213e;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-text p {
    font-size: 18px;
    color: #b0b0b0;
    line-height: 1.8;
    margin-bottom: 40px;
}

.skills {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.skill {
    text-align: left;
}

.skill-name {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 8px;
}

.skill-bar {
    width: 100%;
    height: 8px;
    background-color: #1a1a2e;
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff, #0099cc);
    border-radius: 4px;
    transition: width 1s ease-out;
}

/* Services Section */
.services {
    padding: 100px 0;
    background-color: #1a1a2e;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.service-card {
    background-color: #16213e;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #2a2a4e;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
    border-color: #00d4ff;
}

.service-icon {
    font-size: 48px;
    color: #00d4ff;
    margin-bottom: 20px;
}

.service-card h3 {
    font-size: 24px;
    color: #ffffff;
    margin-bottom: 15px;
}

.service-card p {
    color: #b0b0b0;
    line-height: 1.6;
}

/* Portfolio Section */
.portfolio {
    padding: 100px 0;
    background-color: #16213e;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.portfolio-item {
    background-color: #1a1a2e;
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #2a2a4e;
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 212, 255, 0.2);
}

.portfolio-image {
    height: 200px;
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    color: #1a1a2e;
}

.portfolio-content {
    padding: 30px;
}

.portfolio-content h3 {
    font-size: 22px;
    color: #ffffff;
    margin-bottom: 10px;
}

.portfolio-content p {
    color: #b0b0b0;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background-color: #1a1a2e;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 20px;
    font-size: 18px;
    color: #b0b0b0;
}

.contact-item i {
    color: #00d4ff;
    font-size: 24px;
    width: 30px;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-form input,
.contact-form textarea {
    padding: 15px;
    border: 1px solid #2a2a4e;
    border-radius: 8px;
    background-color: #16213e;
    color: #ffffff;
    font-size: 16px;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #00d4ff;
}

.submit-btn {
    background-color: #00d4ff;
    color: #1a1a2e;
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
}

/* Footer */
.footer {
    background-color: #16213e;
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid #2a2a4e;
}

.footer p {
    color: #b0b0b0;
}

/* Animations */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .name {
        font-size: 36px;
    }
    
    .title {
        font-size: 24px;
    }
    
    .nav-list {
        flex-direction: column;
        gap: 15px;
    }
    
    .header .container {
        flex-direction: column;
        gap: 20px;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .portfolio-grid {
        grid-template-columns: 1fr;
    }
    
    .image-container {
        width: 250px;
        height: 250px;
    }
    
    .profile-image {
        font-size: 80px;
    }
}