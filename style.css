Reset and Base Styles
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(102, 126, 234, 0.95);
    backdrop-filter: blur(15px);
    z-index: 1000;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo h2 {
    color: #ff6b6b;
    font-size: 24px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #ff6b6b;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ff6b6b, #feca57);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding-top: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 107, 107, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(254, 202, 87, 0.2) 0%, transparent 50%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-text {
    animation: fadeInLeft 1s ease-out;
}

.greeting {
    font-size: 28px;
    color: #ffffff;
    margin-bottom: 10px;
    font-weight: normal;
}

.name {
    font-size: 52px;
    color: #ffffff;
    margin-bottom: 15px;
    font-weight: bold;
    letter-spacing: -1px;
}

.title {
    font-size: 32px;
    color: #ffffff;
    margin-bottom: 25px;
    font-weight: 600;
}

.highlight {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

.description {
    font-size: 18px;
    color: #b0b0b0;
    margin-bottom: 30px;
    line-height: 1.8;
}

.social-links {
    display: flex;
    gap: 15px;
    margin-bottom: 30px;
}

.social-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    border: 2px solid #ff6b6b;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ff6b6b;
    text-decoration: none;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.social-icon:hover {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: #ffffff;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.cta-button {
    display: inline-block;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: #ffffff;
    padding: 15px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
    border: none;
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6);
    background: linear-gradient(45deg, #feca57, #ff6b6b);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out;
}

.image-container {
    position: relative;
    width: 350px;
    height: 350px;
}

.profile-image {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 120px;
    color: #ffffff;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
}

.profile-image::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, #ff6b6b, #feca57, transparent);
    animation: rotate 3s linear infinite;
}

.profile-image::after {
    content: '';
    position: absolute;
    inset: 5px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
    z-index: 1;
}

.profile-image i {
    position: relative;
    z-index: 2;
}

/* Section Styles */
.section-title {
    font-size: 42px;
    text-align: center;
    margin-bottom: 60px;
    color: #ffffff;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #ff6b6b, #feca57);
    border-radius: 2px;
}

/* About Section */
.about {
    padding: 100px 0;
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-text p {
    font-size: 18px;
    color: #b0b0b0;
    line-height: 1.8;
    margin-bottom: 40px;
}

.skills {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.skill {
    text-align: left;
}

.skill-name {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 8px;
}

.skill-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: linear-gradient(90deg, #ff6b6b, #feca57, #48cae4);
    border-radius: 4px;
    transition: width 1s ease-out;
    box-shadow: 0 2px 10px rgba(255, 107, 107, 0.3);
}

/* Services Section */
.services {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.service-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.3);
    border-color: #ff6b6b;
    background: rgba(255, 255, 255, 0.15);
}

.service-icon {
    font-size: 48px;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
}

.service-card h3 {
    font-size: 24px;
    color: #ffffff;
    margin-bottom: 15px;
}

.service-card p {
    color: #b0b0b0;
    line-height: 1.6;
}

/* Portfolio Section */
.portfolio {
    padding: 100px 0;
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.portfolio-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(255, 107, 107, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.portfolio-image {
    height: 200px;
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48cae4 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    color: #ffffff;
}

.portfolio-content {
    padding: 30px;
}

.portfolio-content h3 {
    font-size: 22px;
    color: #ffffff;
    margin-bottom: 10px;
}

.portfolio-content p {
    color: #b0b0b0;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 20px;
    font-size: 18px;
    color: #b0b0b0;
}

.contact-item i {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 24px;
    width: 30px;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.contact-form input,
.contact-form textarea {
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: #ffffff;
    font-size: 16px;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #ff6b6b;
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);
}

.submit-btn {
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    color: #ffffff;
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    background: linear-gradient(45deg, #feca57, #ff6b6b);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    padding: 20px 0;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer p {
    color: #b0b0b0;
}

/* Animations */
@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */

/* Mobile Menu Toggle */
.menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.menu-toggle span {
    width: 25px;
    height: 3px;
    background: linear-gradient(45deg, #ff6b6b, #feca57);
    transition: all 0.3s ease;
}

/* Large Tablets and Small Desktops */
@media (max-width: 1024px) {
    .container {
        padding: 0 15px;
    }

    .hero-content {
        gap: 40px;
    }

    .name {
        font-size: 46px;
    }

    .title {
        font-size: 28px;
    }

    .greeting {
        font-size: 24px;
    }

    .image-container {
        width: 300px;
        height: 300px;
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 30px;
    }

    .portfolio-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

/* Tablets */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 20px;
    }

    .nav-list {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .hero {
        padding-top: 120px;
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .name {
        font-size: 36px;
    }

    .title {
        font-size: 24px;
    }

    .greeting {
        font-size: 20px;
    }

    .description {
        font-size: 16px;
    }

    .image-container {
        width: 250px;
        height: 250px;
    }

    .profile-image {
        font-size: 80px;
    }

    .section-title {
        font-size: 32px;
        margin-bottom: 40px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .service-card {
        padding: 30px 20px;
    }

    .portfolio-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .portfolio-item {
        margin: 0 10px;
    }

    .about, .services, .portfolio, .contact {
        padding: 60px 0;
    }
}

/* Mobile Phones */
@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .header {
        padding: 10px 0;
    }

    .logo h2 {
        font-size: 20px;
    }

    .nav-list {
        gap: 10px;
    }

    .nav-link {
        font-size: 14px;
    }

    .hero {
        padding-top: 100px;
        min-height: 90vh;
    }

    .hero-content {
        gap: 30px;
    }

    .greeting {
        font-size: 18px;
    }

    .name {
        font-size: 28px;
        line-height: 1.2;
    }

    .title {
        font-size: 20px;
    }

    .description {
        font-size: 14px;
        line-height: 1.6;
    }

    .social-links {
        gap: 10px;
        justify-content: center;
    }

    .social-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .cta-button {
        padding: 12px 24px;
        font-size: 14px;
    }

    .image-container {
        width: 200px;
        height: 200px;
    }

    .profile-image {
        font-size: 60px;
    }

    .section-title {
        font-size: 26px;
        margin-bottom: 30px;
    }

    .about-text p {
        font-size: 16px;
    }

    .service-card {
        padding: 25px 15px;
    }

    .service-icon {
        font-size: 36px;
    }

    .service-card h3 {
        font-size: 20px;
    }

    .service-card p {
        font-size: 14px;
    }

    .portfolio-image {
        height: 150px;
        font-size: 36px;
    }

    .portfolio-content {
        padding: 20px;
    }

    .portfolio-content h3 {
        font-size: 18px;
    }

    .portfolio-content p {
        font-size: 14px;
    }

    .contact-item {
        font-size: 16px;
        gap: 15px;
    }

    .contact-item i {
        font-size: 20px;
        width: 25px;
    }

    .contact-form input,
    .contact-form textarea {
        padding: 12px;
        font-size: 14px;
    }

    .submit-btn {
        padding: 12px 24px;
        font-size: 14px;
    }

    .about, .services, .portfolio, .contact {
        padding: 40px 0;
    }
}

/* Extra Small Devices */
@media (max-width: 320px) {
    .name {
        font-size: 24px;
    }

    .title {
        font-size: 18px;
    }

    .image-container {
        width: 180px;
        height: 180px;
    }

    .profile-image {
        font-size: 50px;
    }

    .section-title {
        font-size: 22px;
    }

    .service-card {
        padding: 20px 10px;
    }

    .portfolio-content {
        padding: 15px;
    }
}